import api from "./index.js"

//获取机型
export const getAircraftStyle = (params) => api._post(
    '/wechat/Aircraft/queryAll', {
        params
    })
//查询所有航线信息
export const getRouteAll = (query) => api._post('/wechat/route/queryAll', {
    query
})
//查询所有空域信息
export const getAirspaceAll = (query) => api._post('/wechat/airspace/queryAll', {
    query
})
//查询任务类型
export const getFlightPurpose = (query) => api._post('/wechat/flightPurpose/queryAll', {
    query
})
