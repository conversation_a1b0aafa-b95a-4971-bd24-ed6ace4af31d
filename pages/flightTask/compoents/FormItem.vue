<template>
  <view class="form-item" :class="formItemClasses">
    <view class="label-box" :style="labelStyle" v-show="!!label">
      {{ label }}
    </view>
    <view class="value-box" :style="valueBoxStyle">
      <slot></slot>
      <van-icon
        name="close"
        class="clear-icon"
        v-show="clearable"
        @click.stop="onClear()"
      />
    </view>
    <van-icon name="arrow-down" v-if="showIcon" class="right-icon" />
  </view>
</template>
<script>
export default {
  name: 'FormItem',
  props: {
    required: Boolean,
    label: String,
    showIcon: Boolean,
    labelWidth: {
      type: String,
      default: '90px',
    },
    multiLine: Boolean, //标题换行
    isChild: Boolean, //是否是子组件
    clearable: Boolean, //是否可清除
    onClear: {
      type: Function,
      default: () => {},
    },
  },
  computed: {
    formItemClasses() {
      return `${this.required ? 'required' : ''}
      ${this.multiLine ? 'multi-line' : ''}
      ${this.isChild ? 'is-child' : ''}`
    },
    // 标签样式
    labelStyle() {
      if (this.multiLine) {
        return 'width:100%'
      }
      return `width:${this.labelWidth}`
    },
    // 值容器样式
    valueBoxStyle() {
      if (this.multiLine) {
        return 'width:100%'
      }
      return `width: calc(100% - ${this.labelWidth} - 15px)`
    },
  },
}
</script>

<style scoped lang="scss">
.form-item {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  //border-bottom: 1px solid #eee;
  flex-wrap: wrap;
  position: relative;
  box-sizing: border-box;
  min-height: 36px;

  .label-box {
    font-size: 14px;
    color: #323233;
    word-break: break-all;
    position: relative;
    padding: 8px 0;
  }

  .value-box {
    border-bottom: 1px solid #eee;
    padding: 8px 0;
    position: relative;
  }
}

.multi-line {
  display: block;
  padding: 8px 0;

  .label-box {
    width: 100%;
    text-align: left;
    padding: 0;
  }

  .value-box {
    width: 100%;
    padding: 0;
  }

  > .value-box {
    border-bottom: none;
  }
}

.is-child {
  padding: 8px 16px;

  &:last-child {
    border-bottom: none;
  }
}

.required {
  .label-box::before {
    content: '*';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    color: darkred;
    font-size: 14px;
  }
}

.right-icon {
  color: #999;
  font-size: 12px;
  flex-shrink: 0;
}

.clear-icon {
  color: #999;
  font-size: 12px;
  flex-shrink: 0;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 9;
}
</style>
