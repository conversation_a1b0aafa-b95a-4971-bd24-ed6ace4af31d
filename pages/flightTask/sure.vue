<template>
  <view class="receive-page">
    <!-- 自定义导航栏 -->
    <CustomerNav title="待确认任务"/>

    <!-- 任务列表 -->
    <view class="task-list">
      <view class="task-card" v-for="(task, index) in taskList" :key="index">
        <view class="task-info">
          <view class="info-row">
            <view class="info-item">
              <text class="label">注册号：</text>
              <text class="value">{{ task.registrationNumber }}</text>
            </view>
            <view class="info-item">
              <text class="label">任务性质：</text>
              <text class="value">{{ task.taskType }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item">
              <text class="label">起飞基地：</text>
              <text class="value">{{ task.departureBase }}</text>
            </view>
          </view>
          <view class="info-row">
            <view class="info-item">
              <text class="label">预估架次：</text>
              <text class="value">{{ task.estimatedFlights }}</text>
            </view>
          </view>

          <view class="info-row" v-if="task.landingBase">
            <view class="info-item">
              <text class="label">降落基地：</text>
              <text class="value">{{ task.landingBase }}</text>
            </view>
          </view>

          <view class="info-row" v-if="task.stopoverBases">
            <view class="info-item full-width">
              <text class="label">经停基地：</text>
              <text class="value">{{ task.stopoverBases }}</text>
            </view>
          </view>

          <view class="info-row">
            <view class="info-item full-width">
              <text class="label">飞行日期：</text>
              <text class="value">{{ task.flightDate }}</text>
            </view>
          </view>


          <view class="info-row">
            <view class="info-item full-width">
              <text class="label">市场部备注：</text>
              <text class="value">{{ task.marketingRemarks }}</text>
            </view>
          </view>

          <view class="publish-time">
            <text>发布时间：{{ task.publishTime }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import CustomerNav from "../../components/CutomerNav/index.vue";
import dayjs from 'dayjs';

export default {
  name: "sure",
  components: {CustomerNav},
  data() {
    return {
      taskList: [
        {
          registrationNumber: 'B-7613',
          taskType: '空中游览',
          departureBase: '星野基地',
          estimatedFlights: '3',
          flightDate: '2025-07-31（明天）',
          marketingRemarks: '有外国人',
          publishTime: '2025-07-31 16:50:28'
        },
        {
          registrationNumber: 'B-7613',
          taskType: '调机',
          departureBase: '星野基地',
          landingBase: '星野基地',
          stopoverBases: '星野基地、星野基地、星野基地',
          flightDate: '2025-07-31（明天）',
          marketingRemarks: '有外国人',
          publishTime: '2025-07-31 16:50:28'
        }
      ]
    }
  },
  mounted() {
    this.loadTasks();
  },
  methods: {
    // 加载任务列表
    loadTasks() {
      // 这里可以调用API获取真实数据
      console.log('加载待接收任务列表');
    },

    // 接收任务
    acceptTask(task) {
      uni.showModal({
        title: '确认接收',
        content: `确定要接收注册号为 ${task.registrationNumber} 的任务吗？`,
        success: (res) => {
          if (res.confirm) {
            // 调用接收任务的API
            uni.showToast({
              title: '任务接收成功',
              icon: 'success'
            });
          }
        }
      });
    }
  }
}
</script>

<style scoped>
@import "../../assets/css/common.less";

.receive-page {
  background-color: #f7f7f7;
  min-height: 100vh;
}

.task-list {
  padding: 16px;
}

.task-card {
  background-color: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.task-info {
  padding: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.info-row:last-of-type {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 50%;
  margin-bottom: 8px;
}

.info-item.full-width {
  flex: 1;
  min-width: 100%;
}

.label {
  color: #666666;
  font-size: 14px;
  margin-right: 4px;
  white-space: nowrap;
}

.value {
  color: #333333;
  font-size: 14px;
  font-weight: 500;
}

.publish-time {
  text-align: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}

.publish-time text {
  color: #666666;
  font-size: 12px;
}

/* 响应式调整 */
@media (max-width: 375px) {
  .info-item {
    min-width: 100%;
  }

  .task-info {
    padding: 16px;
  }
}
</style>
