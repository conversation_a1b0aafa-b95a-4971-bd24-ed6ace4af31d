<template>
  <view class="add-fight-task">
    <!-- 自定义导航栏 -->
    <CustomerNav title="创建飞行任务书" />

    <!-- 内容主体 -->
    <view class="content">
      <view class="form-box">
        <FormItem label="注册号" required show-icon>
          <input
            class="input-box"
            v-model="formData.registrationNumber.text"
            placeholder="请选择注册号"
            disabled
            @click="
              openPicker('注册号', registrationOptions, 'registrationNumber')
            "
          />
        </FormItem>
        <FormItem label="机型" required>
          <input
            class="input-box"
            v-model="formData.registrationNumber.value"
            placeholder="请选择机型"
            disabled
          />
        </FormItem>
        <FormItem label="任务性质" required>
          <view>
            <view class="btn-groups">
              <view
                class="custom-tag"
                v-for="item in taskTypeList"
                :class="formData.taskTypeValue === item.value ? 'active' : ''"
                :key="item.value"
                @click="onTaskTypeChange(item)"
              >
                {{ item.text }}
              </view>
            </view>

            <view
              class="input-box-line"
              v-if="formData.taskTypeValue === '其他'"
            >
              <input
                v-model="formData.taskType"
                placeholder="请选择"
                disabled
                @click="openPicker('任务性质', flightPurposeList, 'taskType')"
              />
              <van-icon name="arrow-down" class="right-icon" />
            </view>
          </view>
        </FormItem>
        <FormItem label="计划时间" required>
          <view>
            <view class="btn-groups">
              <view
                class="custom-tag"
                v-for="item in planTimeList"
                :class="formData.flightDateValue === item.value ? 'active' : ''"
                :key="item.value"
                @click="onPlanTimeChange(item)"
              >
                {{ item.text }}
              </view>
            </view>

            <view
              class="input-box-line"
              v-if="formData.flightDateValue === '其他日期'"
            >
              <input
                v-model="formData.flightDate"
                placeholder="请选择"
                disabled
                @click="datePickerShow = true"
              />
              <van-icon name="arrow-down" class="right-icon" />
            </view>
          </view>
        </FormItem>
        <view v-if="formData.taskType.indexOf('空中游览') > -1">
          <FormItem label="空游产品名称" show-icon>
            <input
              class="input-box"
              v-model="formData.productName.text"
              placeholder="请选择空中游览产品名称"
              disabled
              @click="openPicker('空游产品名称', [], 'productName')"
            />
          </FormItem>
          <FormItem label="套餐名称" show-icon>
            <input
              class="input-box"
              v-model="formData.packageName.text"
              placeholder="请选择套餐名称"
              disabled
              @click="openPicker('套餐名称', [], 'packageName')"
            />
          </FormItem>
        </view>
        <view v-else>
          <FormItem label="航线类型">
            <view class="btn-groups">
              <view
                class="custom-tag"
                v-for="item in routeTypeList"
                :class="formData.flightType === item.value ? 'active' : ''"
                :key="item.value"
                @click="onRouteTypeChange(item)"
              >
                {{ item.text }}
              </view>
            </view>
          </FormItem>
          <view v-if="formData.flightType === 1">
            <FormItem
              label="航线选择"
              show-icon
              clearable
              :on-clear="onRouteClear"
            >
              <input
                class="input-box"
                v-model="formData.routeOrAirspaceName.text"
                placeholder="请选择航线"
                disabled
                @click="openPicker('航线', routeOptions, 'routeOrAirspaceName')"
              />
            </FormItem>
            <FormItem
              label="航线手动输入"
              multi-line
              v-if="!formData.routeOrAirspaceName.text"
            >
              <view>
                <FormItem label="起飞基地" is-child label-width="74px">
                  <view class="flex-row">
                    <input
                      class="input-box"
                      v-model="formData.departure.text"
                      placeholder="请选择"
                      disabled
                      @click="openPicker('起飞基地', baseOptions, 'departure')"
                    />
                  </view>
                </FormItem>
                <FormItem label="经停点" is-child label-width="74px">
                  <view class="flex-row">
                    <input
                      class="input-box"
                      v-model="formData.alternate.text"
                      placeholder="请选择"
                      disabled
                      @click="openPicker('经停点', baseOptions, 'alternate')"
                    />
                  </view>
                </FormItem>
                <FormItem label="降落基地" is-child label-width="74px">
                  <view class="flex-row">
                    <input
                      class="input-box"
                      v-model="formData.arrive.text"
                      placeholder="请选择"
                      disabled
                      @click="openPicker('降落基地', baseOptions, 'arrive')"
                    />
                  </view>
                </FormItem>
              </view>
            </FormItem>
          </view>
          <FormItem label="空域选择" required show-icon v-else>
            <input
              class="input-box"
              v-model="formData.routeOrAirspaceName.text"
              placeholder="请选择空域"
              disabled
              @click="
                openPicker('空域', airspaceOptions, 'routeOrAirspaceName')
              "
            />
          </FormItem>
        </view>

        <FormItem label="预计架次" required>
          <input
            class="input-box"
            v-model="formData.flightFrequency"
            placeholder="请输入预计架次"
            type="number"
          />
        </FormItem>
        <FormItem label="计划起降时间" multi-line>
          <view>
            <FormItem
              :label="'架次' + (index + 1)"
              label-width="74px"
              required
              is-child
              v-for="(item, index) in formData.takeOffAndLanding"
              :key="index"
              class="text-row"
            >
              <view class="flex-row">
                <input
                  class="input-box"
                  v-model="item.planDepartTime"
                  placeholder="预计起飞时间"
                />
                <input
                  class="input-box"
                  v-model="item.planArriveTime"
                  placeholder="预计降落时间"
                />
              </view>
            </FormItem>
          </view>
        </FormItem>
        <FormItem label="起降间隔">
          <view>
            <view class="btn-groups">
              <view
                class="custom-tag"
                v-for="item in timeIntervalList"
                :class="formData.timeInterval === item.value ? 'active' : ''"
                :key="item.value"
                @click="onTimeIntervalChange(item)"
              >
                {{ item.text }}
              </view>
            </view>
          </view>
        </FormItem>
        <FormItem label="备注">
          <input
            class="input-box"
            v-model="formData.remark"
            placeholder="请输入备注"
          />
        </FormItem>
        <!--        <FormItem label="智能解析" multi-line>-->
        <!--          <textarea-->
        <!--              v-model="formData.aircraftType"-->
        <!--              placeholder="请输入"-->
        <!--              :maxlength="-1"-->
        <!--              class="textarea-box"-->
        <!--          />-->
        <!--        </FormItem>-->
        <!--        <view class="flex-right-row mt-8">-->
        <!--          <van-button type="info" plain size="small">解析</van-button>-->
        <!--        </view>-->
        <view class="submit-btn">
          <van-button type="info" @click="submitForm"
            >确定创建飞行计划
          </van-button>
        </view>
      </view>
    </view>

    <!-- 下拉选择-->
    <van-popup :show="pickerData.show" position="bottom">
      <van-picker
        :columns="pickerData.list"
        @confirm="onPickerConfirm"
        @cancel="closePicker"
        show-toolbar
        :title="pickerData.title"
      />
    </van-popup>
    <!-- 日期选择器 -->
    <van-calendar
      :show="datePickerShow"
      @close="datePickerShow = false"
      @confirm="onDateConfirm"
    />
  </view>
</template>

<script>
import CustomerNav from '../../components/CutomerNav/index.vue'
import FormItem from './compoents/FormItem.vue'
import dayjs from 'dayjs'
import { DATE_FORMAT, SUCCESS_CODE } from '../../utils/constant'
import {
  addFlightTask,
  getAircraftStyle,
  getAirportAll,
  getAirspaceAll,
  getFlightPurpose,
  getRouteAll,
} from '../../api/flightTask'

export default {
  name: 'createFlightTask',
  components: { FormItem, CustomerNav },
  data() {
    return {
      // 表单数据
      formData: {
        registrationNumber: { text: '', value: '' }, // 注册号
        aircraftType: '', // 机型
        taskType: '空中交通', // 任务性质
        taskTypeValue: '空中交通', //任务性质选中按钮的值--页面展示用
        flightDate: '', //计划时间
        flightDateValue: '', //计划时间选中按钮的值--页面展示用
        timeInterval: '', //  起降间隔--页面展示用
        productName: { text: '', value: '' }, //产品名称
        packageName: { text: '', value: '' }, //套餐名称
        flightFrequency: 1, //架次
        takeOffAndLanding: [{ planArriveTime: '', planDepartTime: '' }], //起降时间
        remark: '',
        flightType: 1, //航线类型
        routeOrAirspaceName: { text: '', value: '' }, //航线选择
        departure: { text: '', value: '' }, //起飞基地
        alternate: { text: '', value: '' }, //经停点
        arrive: { text: '', value: '' }, //降落基地
      },
      //机型
      registrationOptions: [],
      //任务类型
      taskTypeList: [
        { text: '空中游览', value: '空中游览' },
        { text: '空中交通', value: '空中交通' },
        { text: '其他', value: '其他' },
      ],
      //任务性质
      flightPurposeList: [],
      //计划时间
      planTimeList: [
        { text: '今天', value: '今天' },
        { text: '明天', value: '明天' },
        { text: '其他日期', value: '其他日期' },
      ],
      //起降间隔
      timeIntervalList: [
        { text: '5分钟', value: 5 },
        { text: '15分钟', value: 15 },
        { text: '20分钟', value: 20 },
        { text: '30分钟', value: 30 },
      ],
      //航线类型
      routeTypeList: [
        { text: '航线', value: 1 },
        { text: '空域', value: 2 },
      ],
      routeOptions: [], //航线
      airspaceOptions: [], //空域
      baseOptions: [], //起飞/降落/经停点基地
      //下拉选择数据
      pickerData: {
        show: false,
        title: '',
        list: [],
        formKey: '',
      },
      datePickerShow: false,
    }
  },
  created() {
    this.getPickerListData()
  },
  watch: {
    'formData.flightFrequency'(newVal) {
      this.formData.takeOffAndLanding = Array.from(
        { length: Number(newVal) },
        () => ({
          planArriveTime: '',
          planDepartTime: '',
        })
      )
    },
  },
  methods: {
    openPicker(title, list, formKey) {
      this.pickerData = {
        show: true,
        title: title,
        list: list,
        formKey: formKey,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: '',
        list: [],
        formKey: '',
      }
    },
    onPickerConfirm(ev) {
      this.formData[this.pickerData.formKey] = ev.detail.value
      // if (this.pickerData.formKey === "registrationNumber") {
      //   this.formData.aircraftType = ev.detail.value.value;
      //   this.formData.registrationNumber = ev.detail.value.text;
      // }
      if (this.pickerData.formKey === 'taskType') {
        this.formData.taskType = ev.detail.value.text
      }
      this.closePicker()
    },
    onDateConfirm(ev) {
      this.formData.flightDate = dayjs(ev.detail).format(DATE_FORMAT)
      this.datePickerShow = false
    },
    //任务性质按钮
    onTaskTypeChange(item) {
      this.formData.taskTypeValue = item.value
      // this.formData.taskType = item.text;
    },
    //计划时间按钮
    onPlanTimeChange(item) {
      this.formData.flightDateValue = item.value
      if (item.value === '今天') {
        this.formData.flightDate = dayjs().format(DATE_FORMAT)
      } else if (item.value === '明天') {
        this.formData.flightDate = dayjs().add(1, 'day').format(DATE_FORMAT)
      }
    },
    //航线清除
    onRouteClear() {
      console.log(111)
      this.formData.routeOrAirspaceName = { text: '', value: '' }
    },
    //起降时间间隔
    onTimeIntervalChange(item) {
      this.formData.timeInterval = item.value
      const intervalMinutes = parseInt(item.value) // 提取间隔分钟数

      this.formData.takeOffAndLanding.map((item) => {
        if (item.planDepartTime) {
          // 使用dayjs处理时间格式转换和计算
          const departTime = item.planDepartTime
          const hours = departTime.slice(0, 2)
          const minutes = departTime.slice(2, 4)

          // 创建dayjs时间对象并加上间隔分钟数
          const arriveTime = dayjs(
            `${this.formData.flightDate} ${hours}:${minutes}`
          )
            .add(intervalMinutes, 'minute')
            .format('HHmm')

          item.planArriveTime = arriveTime
        }
      })
    },
    //航线类型
    onRouteTypeChange(item) {
      this.formData.flightType = item.value
      this.formData.routeOrAirspaceName = { text: '', value: '' }
      this.formData.departure = { text: '', value: '' }
      this.formData.alternate = { text: '', value: '' }
      this.formData.arrive = { text: '', value: '' }
    },
    //获取数据
    async getPickerListData() {
      //任务性质
      const res = await getFlightPurpose()
      if (res.response.code === SUCCESS_CODE) {
        this.flightPurposeList = res.response.data.map((item) => {
          return {
            text: item.name || '',
            value: item.id || '',
          }
        })
      }
      // 机型机号
      const res2 = await getAircraftStyle()
      if (res2.response.code === SUCCESS_CODE) {
        this.registrationOptions = res2.response.data.map((item) => {
          return {
            text: item.aircraftTailNo || '',
            value: item.aircraftStyle || '',
          }
        })
      }
      //航线
      if (this.formData.flightType === 1) {
        const res3 = await getRouteAll()
        if (res3.response.code === SUCCESS_CODE) {
          this.routeOptions = res3.response.data.map((item) => {
            return {
              text: item.routeName || '',
              value: item.id || '',
            }
          })
        }
        const res5 = await getAirportAll()
        if (res5.response.code === SUCCESS_CODE) {
          this.baseOptions = res5.response.data.map((item) => {
            return {
              text: item.name || '',
              value: item.threeAirportCode || '',
            }
          })
        }
      }
      //空域
      if (this.formData.flightType === 2) {
        const res4 = await getAirspaceAll()
        if (res4.response.code === SUCCESS_CODE) {
          this.airspaceOptions = res4.response.data.map((item) => {
            return {
              text: item.airspaceName || '',
              value: item.id || '',
            }
          })
        }
      }
    },
    //表单校验
    validateForm() {
      // 表单验证
      if (!this.formData.registrationNumber.text) {
        uni.showToast({
          title: '请选择注册号',
          icon: 'none',
        })
        return false
      }

      if (!this.formData.taskType) {
        uni.showToast({
          title: '请选择任务性质',
          icon: 'none',
        })
        return false
      }

      if (!this.formData.flightDate) {
        uni.showToast({
          title: '请选择计划时间',
          icon: 'none',
        })
        return false
      }
      if (!this.formData.flightFrequency) {
        uni.showToast({
          title: '请输入预计架次',
          icon: 'none',
        })
        return false
      }
      if (!this.formData.takeOffAndLanding?.length < 0) {
        uni.showToast({
          title: '请输入计划起降时间',
          icon: 'none',
        })
        return false
      }

      return true
    },

    // 提交表单
    async submitForm() {
      if (!this.validateForm()) return
      const params = {
        aircraftType: this.formData.registrationNumber.value,
        alternate: this.formData.alternate.text,
        alternateCode: this.formData.alternate.value,
        arrive: this.formData.arrive.text,
        arriveCode: this.formData.arrive.value,
        departure: this.formData.departure.text,
        departureCode: this.formData.departure.value,
        flightDate: this.formData.flightDate,
        flightFrequency: this.formData.flightFrequency,
        flightType: this.formData.flightType,
        packageName: this.formData.packageName.text,
        productName: this.formData.productName.text,
        registrationNumber: this.formData.registrationNumber.text,
        remark: this.formData.remark,
        routeOrAirspaceId: this.formData.routeOrAirspaceName.value,
        routeOrAirspaceName: this.formData.routeOrAirspaceName.text,
        takeOffAndLanding: this.formData.takeOffAndLanding,
        taskType: this.formData.taskType,
      }
      const { response } = await addFlightTask(params)
      if (response.code === SUCCESS_CODE) {
        uni.showToast({
          title: '创建计划成功',
          icon: 'success',
        })
        setTimeout(() => {
          uni.navigateBack()
        }, 1500)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.add-fight-task {
  width: 100%;
  min-height: 100vh;
  background-color: #f7f7f7;
}

// 内容区域
.content {
  box-sizing: border-box;
  padding: 16px;

  .form-box {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }

  .btn-groups {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
  }

  .custom-tag {
    padding: 0 4px;
    height: 24px;
    line-height: 24px;
    border: 1px solid #1989fa;
    color: #1989fa;
    background: transparent;
    font-size: 12px;
    border-radius: 4px;

    &.active {
      background: #1989fa;
      color: #fff;
    }
  }

  input {
    font-size: 14px;
    font-weight: normal;

    &::placeholder {
      font-weight: normal;
      color: #999;
    }
  }

  .input-box-line {
    padding: 4px;
    //border: 1px solid #999;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
  }

  .mt-8 {
    margin-top: 8px;
  }

  .right-icon {
    color: #999;
    font-size: 12px;
  }

  .flex-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
  }

  /deep/ .text-row {
    .value-box {
      border-bottom: none;
    }

    .input-box {
      border-bottom: 1px solid #ccc;
    }
  }

  .flex-right-row {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .textarea-box {
    width: 100%;
    margin-top: 8px;
    border: 1px solid #eee;
    box-sizing: border-box;
    padding: 8px;
    border-radius: 4px;
  }

  .submit-btn {
    width: 100%;
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
