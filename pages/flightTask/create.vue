<template>
  <view class="add-fight-task">
    <!-- 自定义导航栏 -->
    <CustomerNav title="创建飞行任务书"/>

    <!-- 内容主体 -->
    <view class="content">
      <view class="form-box">
        <FormItem label="注册号" required show-icon>
          <input
              class="input-box"
              v-model="formData.registrationNumber"
              placeholder="请选择注册号"
              disabled
              @click="openPicker('注册号', registrationOptions, 'registrationNumber')"
          />
        </FormItem>
        <FormItem label="机型" required>
          <input
              class="input-box"
              v-model="formData.aircraftType"
              placeholder="请选择机型"
              disabled
          />
        </FormItem>
        <FormItem label="任务性质" required>
          <view>
            <view class="btn-groups">
              <view
                  class="custom-tag"
                  v-for="item in taskTypeList"
                  :class="formData.taskTypeValue === item.value ? 'active' :''"
                  :key="item.value"
                  @click="onTaskTypeChange(item)"
              >
                {{ item.text }}
              </view>
            </view>


            <view class="input-box-line" v-if="formData.taskTypeValue==='其他'">
              <input
                  v-model="formData.taskType"
                  placeholder="请选择"
                  disabled
                  @click="openPicker('任务性质', flightPurposeList, 'taskType')"
              />
              <van-icon name="arrow-down" class="right-icon"/>
            </view>
          </view>
        </FormItem>
        <FormItem label="计划时间" required>
          <view>
            <view class="btn-groups">
              <view
                  class="custom-tag"
                  v-for="item in planTimeList"
                  :class="formData.flightDateValue === item.value ? 'active' :''"
                  :key="item.value"
                  @click="onPlanTimeChange(item)"
              >
                {{ item.text }}
              </view>
            </view>

            <view class="input-box-line" v-if="formData.flightDateValue === '其他日期'">
              <input
                  v-model="formData.flightDate"
                  placeholder="请选择"
                  disabled
                  @click="datePickerShow = true"

              />
              <van-icon name="arrow-down" class="right-icon"/>
            </view>
          </view>
        </FormItem>
        <view v-if="formData.taskType.indexOf('空中游览') > -1">
          <FormItem label="空游产品名称" required show-icon>
            <input
                class="input-box"
                v-model="formData.productName"
                placeholder="请选择空中游览产品名称"
                disabled
                @click="openPicker('空游产品名称', [], 'productName')"
            />
          </FormItem>
          <FormItem label="套餐名称" required show-icon>
            <input
                class="input-box"
                v-model="formData.packageName"
                placeholder="请选择套餐名称"
                disabled
                @click="openPicker('套餐名称', [], 'packageName')"
            />
          </FormItem>
        </view>
        <view v-else>
          <FormItem label="航线类型" required>
            <view class="btn-groups">
              <view
                  class="custom-tag"
                  v-for="item in routeTypeList"
                  :class="formData.flightType === item.value ? 'active' :''"
                  :key="item.value"
                  @click="onRouteTypeChange(item)"
              >
                {{ item.text }}
              </view>
            </view>
          </FormItem>
          <view v-if="formData.flightType === 1">
            <FormItem label="航线选择" required show-icon>
              <input
                  class="input-box"
                  v-model="formData.routeOrAirspaceName"
                  placeholder="请选择航线"
                  disabled
                  @click="openPicker('航线', registrationOptions, 'routeOrAirspaceId')"
              />
            </FormItem>
            <FormItem label="航线手动输入" multi-line>
              <view>
                <FormItem label="起飞基地" is-child>
                  <view class="flex-row">
                    <input
                        class="input-box"
                        v-model="formData.departure"
                        placeholder="请选择"
                        disabled
                        @click="openPicker('起飞基地', registrationOptions, 'routeOrAirspaceId')"
                    />
                  </view>
                </FormItem>
                <FormItem label="经停点" is-child>
                  <view class="flex-row">
                    <input
                        class="input-box"
                        v-model="formData.alternate"
                        placeholder="请选择"
                        disabled
                        @click="openPicker('经停点', registrationOptions, 'routeOrAirspaceId')"
                    />
                  </view>
                </FormItem>
                <FormItem label="降落基地" is-child>
                  <view class="flex-row">
                    <input
                        class="input-box"
                        v-model="formData.arrive"
                        placeholder="请选择"
                        disabled
                        @click="openPicker('降落基地', registrationOptions, 'routeOrAirspaceId')"
                    />
                  </view>
                </FormItem>
              </view>
            </FormItem>
          </view>
          <FormItem label="空域选择" required show-icon v-else>
            <input
                class="input-box"
                v-model="formData.routeOrAirspaceName"
                placeholder="请选择空域"
                disabled
                @click="openPicker('空域', registrationOptions, 'routeOrAirspaceId')"
            />
          </FormItem>
        </view>

        <FormItem label="预计架次" required>
          <input
              class="input-box"
              v-model="formData.flightFrequency"
              placeholder="请输入预计架次"
              type="number"
          />
        </FormItem>
        <FormItem label="计划起降时间" multi-line>
          <view>
            <FormItem :label="'架次' + (index + 1)" label-width="50px" required is-child
                      v-for="(item,index) in formData.takeOffAndLanding"
                      :key="index">
              <view class="flex-row">
                <input
                    class="input-box"
                    v-model="item.planDepartTime"
                    placeholder="预计起飞时间"
                />
                <input
                    class="input-box"
                    v-model="item.planArriveTime"
                    placeholder="预计降落时间"
                />
              </view>
            </FormItem>
          </view>
        </FormItem>
        <FormItem label="起降间隔">
          <view>
            <view class="btn-groups">
              <view
                  class="custom-tag"
                  v-for="item in timeIntervalList"
                  :class="formData.timeInterval === item.value ? 'active' :''"
                  :key="item.value"
                  @click="onTimeIntervalChange(item)"
              >
                {{ item.text }}
              </view>
            </view>
          </view>
        </FormItem>
        <FormItem label="备注">
          <input
              class="input-box"
              v-model="formData.remark"
              placeholder="请输入备注"
          />
        </FormItem>
        <FormItem label="智能解析" multi-line>
          <textarea
              v-model="formData.aircraftType"
              placeholder="请输入"
              :maxlength="-1"
              class="textarea-box"
          />
        </FormItem>
        <view class="flex-right-row mt-8">
          <van-button type="info" plain size="small">解析</van-button>
        </view>
        <view class="submit-btn">
          <van-button type="info" @click="submitForm">确定创建飞行计划</van-button>
        </view>
      </view>

    </view>

    <!-- 下拉选择-->
    <van-popup :show="pickerData.show" position="bottom">
      <van-picker
          :columns="pickerData.list"
          @confirm="onPickerConfirm"
          @cancel="closePicker"
          show-toolbar
          :title="pickerData.title"
      />
    </van-popup>
    <!-- 日期选择器 -->
    <van-calendar :show="datePickerShow"
                  @close="datePickerShow = false"
                  @confirm="onDateConfirm"
    />

  </view>
</template>

<script>
import CustomerNav from "../../components/CutomerNav/index.vue";
import FormItem from "./compoents/FormItem.vue";
import dayjs from "dayjs"
import {DATE_FORMAT, SUCCESS_CODE} from "../../utils/constant";
import {getAircraftStyle, getFlightPurpose, getRouteAll} from "../../api/flightTask";

export default {
  name: "createFlightTask",
  components: {FormItem, CustomerNav},
  data() {
    return {
      // 表单数据
      formData: {
        registrationNumber: '', // 注册号
        aircraftType: '', // 机型
        taskType: '空中交通', // 任务性质
        taskTypeValue: "空中交通",//任务性质选中按钮的值--页面展示用
        flightDate: "",//计划时间
        flightDateValue: "",//计划时间选中按钮的值--页面展示用
        timeInterval: "",//  起降间隔--页面展示用
        productName: "",//产品名称
        packageName: "",//套餐名称
        flightFrequency: 1,//架次
        takeOffAndLanding: [{planArriveTime: "", planDepartTime: ""}],//起降时间
        remark: "",
        flightType: 1,//航线类型
        routeOrAirspaceId: "", //航线选择
        routeOrAirspaceName: "", //航线选择
        departure: "",//起飞基地
        alternate: "",//经停点
        arrive: "",//降落基地
      },
      //机型
      registrationOptions: [],
      //任务类型
      taskTypeList: [
        {text: '空中游览', value: '空中游览'},
        {text: '空中交通', value: '空中交通'},
        {text: '其他', value: '其他'},
      ],
      //任务性质
      flightPurposeList: [],
      //计划时间
      planTimeList: [
        {text: '今天', value: "今天"},
        {text: '明天', value: '明天'},
        {text: '其他日期', value: '其他日期'}
      ],
      //起降间隔
      timeIntervalList: [
        {text: '5分钟', value: 5},
        {text: '15分钟', value: 15},
        {text: '20分钟', value: 20},
        {text: '30分钟', value: 30}
      ],
      //航线类型
      routeTypeList: [
        {text: '航线', value: 1},
        {text: '空域', value: 2},
      ],
      routeOptions: [],//航线
      airspaceOptions: [],//空域
      //下拉选择数据
      pickerData: {
        show: false,
        title: "",
        list: [],
        formKey: "",
      },
      datePickerShow: false,
    }
  },
  created() {
    this.getPickerListData();
  },
  watch: {
    "formData.flightFrequency"(newVal) {
      this.formData.takeOffAndLanding = Array.from({length: Number(newVal)}, () => ({
        planArriveTime: "",
        planDepartTime: ""
      }));
    }
  },
  methods: {
    openPicker(title, list, formKey) {
      this.pickerData = {
        show: true,
        title: title,
        list: list,
        formKey: formKey,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: "",
        list: [],
        formKey: "",
      }
    },
    onPickerConfirm(ev) {
      this.formData[this.pickerData.formKey] = ev.detail.value.value;
      if (this.pickerData.formKey === "registrationNumber") {
        this.formData.aircraftType = ev.detail.value.value;
        this.formData.registrationNumber = ev.detail.value.text;
      }
      if (this.pickerData.formKey === "taskType") {
        this.formData.taskType = ev.detail.value.text;
      }
      this.closePicker();
    },
    onDateConfirm(ev) {
      this.formData.flightDate = dayjs(ev.detail).format(DATE_FORMAT);
      this.datePickerShow = false;
    },
    //任务性质按钮
    onTaskTypeChange(item) {
      this.formData.taskTypeValue = item.value;
      // this.formData.taskType = item.text;
    },
    //计划时间按钮
    onPlanTimeChange(item) {
      this.formData.flightDateValue = item.value;
      if (item.value === "今天") {
        this.formData.flightDate = dayjs().format(DATE_FORMAT);
      } else if (item.value === "明天") {
        this.formData.flightDate = dayjs().add(1, 'day').format(DATE_FORMAT);
      }
    },
    //起降时间间隔
    onTimeIntervalChange(item) {
      this.formData.timeInterval = item.value;
      const intervalMinutes = parseInt(item.value); // 提取间隔分钟数

      this.formData.takeOffAndLanding.map((item) => {
        if (item.planDepartTime) {
          // 使用dayjs处理时间格式转换和计算
          const departTime = item.planDepartTime;
          const hours = departTime.slice(0, 2);
          const minutes = departTime.slice(2, 4);

          // 创建dayjs时间对象并加上间隔分钟数
          const arriveTime = dayjs(`${this.formData.flightDate} ${hours}:${minutes}`)
              .add(intervalMinutes, 'minute')
              .format('HHmm');

          item.planArriveTime = arriveTime;
        }
      })
    },
    //航线类型
    onRouteTypeChange(item) {
      this.formData.flightType = item.value;
      this.formData.routeOrAirspaceId = "";
      this.formData.routeOrAirspaceName = "";
      this.formData.departure = "";
      this.formData.alternate = "";
      this.formData.arrive = "";
    },
    //获取数据
    async getPickerListData() {
      //任务性质
      const res = await getFlightPurpose();
      if (res.response.code === SUCCESS_CODE) {
        this.flightPurposeList = res.response.data.map(item => {

          return {
            text: item.name || "",
            value: item.id || ""
          }
        })
      }
      // 机型机号
      const res2 = await getAircraftStyle();
      if (res2.response.code === SUCCESS_CODE) {
        this.registrationOptions = res2.response.data.map(item => {
          return {
            text: item.aircraftTailNo || "",
            value: item.aircraftStyle || ""
          }
        })
      }
      //航线
      if (this.formData.flightType === 1) {
        const res3 = await getRouteAll();
        if (res3.response.code === SUCCESS_CODE) {
          this.routeOptions = res3.response.data.map(item => {
            return {
              text: item.routeName || "",
              value: item.routeCode || ""
            }
          })
        }
      }
      //空域
      if (this.formData.flightType === 2) {
        const res4 = await getRouteAll();
        if (res4.response.code === SUCCESS_CODE) {
          this.airspaceOptions = res4.response.data.map(item => {
            return {
              text: item.airspaceName || "",
              value: item.routeCode || ""
            }
          })
        }
      }
    },

    // 提交表单
    submitForm() {
      // 表单验证
      if (!this.formData.registrationNumber) {
        uni.showToast({
          title: '请选择注册号',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.airspaceProductName) {
        uni.showToast({
          title: '请选择空域产品',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.packageName) {
        uni.showToast({
          title: '请选择套餐',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.expectedFlights) {
        uni.showToast({
          title: '请输入预计航次',
          icon: 'none'
        });
        return;
      }

      // 这里可以调用API提交数据
      console.log('提交的表单数据:', this.formData);

      uni.showToast({
        title: '创建成功',
        icon: 'success'
      });

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  }
}
</script>

<style lang="scss" scoped>
.add-fight-task {
  //background-color: #f5f5f5;
  width: 100%;
  min-height: 100vh;
  box-sizing: border-box;
  padding: 16px;
  background: #fff;
}


// 内容区域
.content {
  padding: 20px 15px;
  margin-top: 88px;

  .btn-groups {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
  }

  .custom-tag {
    padding: 0 4px;
    height: 24px;
    line-height: 24px;
    border: 1px solid #1989fa;
    color: #1989fa;
    background: transparent;
    font-size: 12px;
    border-radius: 4px;

    &.active {
      background: #1989fa;
      color: #fff;
    }
  }

  input {
    font-size: 14px;
    font-weight: normal;

    &::placeholder {
      font-weight: normal;
      color: #999;
    }
  }

  .input-box {
    //border-bottom: 1px solid #eee;
    //box-sizing: border-box;
    //padding-bottom: 6px;
  }

  .input-box-line {
    padding: 4px;
    //border: 1px solid #999;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
  }

  .mt-8 {
    margin-top: 8px;
  }

  .right-icon {
    color: #999;
    font-size: 12px;
  }

  .flex-row {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 6px;
  }

  .flex-right-row {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }

  .textarea-box {
    width: 100%;
    margin-top: 8px;
    border: 1px solid #eee;
    box-sizing: border-box;
    padding: 8px;
    border-radius: 4px;
  }

  .submit-btn {
    width: 100%;
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
