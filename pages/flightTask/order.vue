<template>
  <view>
    <!-- 自定义导航栏 -->
    <CustomerNav title="承运订单"/>
    <view class="order-container">
      <!-- 预约日期选择 -->
      <view class="date-section">
        <view class="section-title">预约日期</view>
        <view class="date-buttons">
          <view
              class="date-btn"
              v-for="(item,index) in dateOptionList"
              :class="{ 'active': selectedDateType === item.value }"
              @click="selectDate(item.value)"
              :key="index"
          >
            {{ item.text }}
          </view>
        </view>
        <view class="input-box-line">
          已选中日期：
          <input
              v-model="selectedDate"
              placeholder="请选择"
              disabled
          />
        </view>
      </view>

      <!-- 订单列表 -->
      <view class="order-list">
        <view class="section-title">订单列表</view>
        <view class="order-card" v-for="(item, index) in orderList" :key="index">
          <!-- 订单列表标题 -->
          <view class="order-status">空中游览</view>

          <!-- 产品信息 -->
          <view class="product-info">
            <view class="info-row">
              <text class="label">产品名称：</text>
              <text class="value">{{ item.productName || '【星野基地】直升机游览' }}</text>
            </view>
            <view class="info-row">
              <text class="label">套餐名称：</text>
              <text class="value">{{ item.packageName || '【日间】15分钟' }}</text>
            </view>
            <view class="info-row">
              <text class="label">旅客：</text>
              <text class="value">{{ item.passengers || '张三、李四' }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 发布需求按钮 -->
      <view class="publish-section">
        <view class="publish-btn" @click="publishOrder">
          发布需求
        </view>
      </view>
    </view>
    <!-- 日期选择器 -->
    <van-calendar :show="datePickerShow"
                  @close="datePickerShow = false"
                  @confirm="onDateConfirm"
    />
  </view>
</template>

<script>
import CustomerNav from "../../components/CutomerNav/index.vue";
import dayjs from 'dayjs';
import {DATE_FORMAT} from "../../utils/constant";

export default {
  name: "order",
  components: {CustomerNav},
  data() {
    return {
      selectedDateType: '今天', // 当前选中的日期类型
      selectedDate: '', // 选中的具体日期
      dateOptionList: [
        {text: '今天', value: "今天"},
        {text: '明天', value: '明天'},
        {text: '后天', value: '后天'},
        {text: '其他日期', value: '其他日期'}
      ],
      datePickerShow: false,
      orderList: [
        {
          productName: '【星野基地】直升机游览',
          packageName: '【日间】15分钟',
          passengers: '张三、李四'
        }
      ]
    }
  },
  mounted() {
    this.initDate();
  },
  methods: {
    // 初始化日期
    initDate() {
      this.selectedDate = dayjs().format('YYYY-MM-DD');
    },

    // 选择日期
    selectDate(type) {
      this.selectedDateType = type;
      switch (type) {
        case '今天':
          this.selectedDate = dayjs().format('YYYY-MM-DD');
          break;
        case '明天':
          this.selectedDate = dayjs().add(1, 'day').format('YYYY-MM-DD');
          break;
        case '后天':
          this.selectedDate = dayjs().add(2, 'day').format('YYYY-MM-DD');
          break;
        case '其他日期':
          this.datePickerShow = true;
          break;
      }
    },

    onDateConfirm(ev) {
      this.selectedDate = dayjs(ev.detail).format(DATE_FORMAT);
      this.datePickerShow = false;
    },

    // 发布订单
    publishOrder() {
      uni.showToast({
        title: '发布需求成功',
        icon: 'success'
      });
    }
  }
}
</script>

<style scoped lang="scss">
@import "../../assets/css/common.less";

.order-container {
  background-color: #fff;
  min-height: 100vh;
  padding: 16px;
}

/* 预约日期选择 */
.date-section {
  margin-bottom: 15px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
}

.date-buttons {
  display: flex;
  gap: 10px;
}

.date-btn {
  flex: 1;
  height: 40px;
  line-height: 40px;
  text-align: center;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  font-size: 14px;
  color: #666;
  transition: all 0.3s;
}

.date-btn.active {
  background-color: #2C5DE5;
  border-color: #2C5DE5;
  color: #fff;
}

/* 订单列表 */
.order-list {
  margin-bottom: 20px;
}

.order-card {
  background-color: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;

  .order-status {
    width: 100%;
    background-color: rgba(44, 93, 229, 1);
    color: #fff;
    font-size: 14px;
    padding: 6px 16px;
    border-radius: 8px 8px 0 0;
  }

  .product-info {
    padding: 16px;
  }
}


.input-box-line {
  padding: 4px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 8px;
  gap: 8px;
  color: #666;
  font-size: 14px;

  .right-icon {
    font-size: 12px;
  }
}


.info-row {
  display: flex;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.label {
  color: #666;
  min-width: 80px;
}

.value {
  color: #333;
  flex: 1;
}


/* 发布按钮 */
.publish-section {
  padding: 16px 0;
}

.publish-btn {
  width: 100%;
  height: 44px;
  line-height: 44px;
  text-align: center;
  background-color: #fff;
  color: #2C5DE5;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  border: 1px solid #2C5DE5;
}

.publish-btn:active {
  background-color: rgba(27, 65, 191, 0.5);
  color: #fff;
  border: 1px solid rgba(27, 65, 191, 0.5);
}
</style>
